import { initializeApp, getApps } from 'firebase/app';
import { getAuth, signInWithEmailAndPassword, Auth } from 'firebase/auth';
import { getDatabase, Database } from 'firebase/database';

// Server-side Firebase configuration
const serverFirebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  databaseURL: process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

// Initialize server-side Firebase app
let serverApp;
let serverAuth: Auth;
let serverDatabase: Database;

if (typeof window === 'undefined') {
  // Only initialize on server-side
  const existingApps = getApps();
  const serverAppName = 'server-app';

  serverApp = existingApps.find(app => app.name === serverAppName) ||
             initializeApp(serverFirebaseConfig, serverAppName);

  serverAuth = getAuth(serverApp);
  serverDatabase = getDatabase(serverApp);
}

// Cached authentication promise to avoid multiple login attempts
let authPromise: Promise<void> | null = null;

// Function to authenticate with server credentials
async function authenticateServer(): Promise<void> {
  if (authPromise) {
    return authPromise;
  }

  authPromise = (async () => {
    try {
      const email = process.env.FIREBASE_ADMIN_EMAIL;
      const password = process.env.FIREBASE_ADMIN_PASSWORD;

      if (!email || !password) {
        throw new Error('Firebase admin credentials not configured');
      }

      await signInWithEmailAndPassword(serverAuth, email, password);
      console.log('Server Firebase authentication successful');
    } catch (error) {
      console.error('Server Firebase authentication failed:', error);
      authPromise = null; // Reset on failure to allow retry
      throw error;
    }
  })();

  return authPromise;
}

// Function to get authenticated database reference
export async function getAuthenticatedDatabase(): Promise<Database> {
  if (typeof window !== 'undefined') {
    throw new Error('This function should only be called on the server side');
  }

  await authenticateServer();
  return serverDatabase;
}
