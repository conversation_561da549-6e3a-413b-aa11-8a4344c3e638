{"name": "utsavfootwear", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@types/nodemailer": "^6.4.17", "cloudinary": "^2.7.0", "firebase": "^12.0.0", "firebase-admin": "^13.5.0", "framer-motion": "^12.23.12", "lucide-react": "^0.536.0", "next": "15.4.4", "nodemailer": "^7.0.5", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "critters": "^0.0.23", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "typescript": "^5"}}